codeunit 60007 "Maxwell Package Trans. Mgt MXW"
{
    SingleInstance = true;

    procedure ProcessBarcode(var PackageTransferHeader: Record "Package Transfer Header MXW")
    var
        PackageNoInformation: Record "Package No. Information";
        PackageTransferLine: Record "Package Transfer Line MXW";
        PackageAlreadyReadErr: Label 'This package has already been read in this package transfer order, please try a different package.';
    begin
        if PackageNoInformation.Get('', '', PackageTransferHeader.Barcode) then begin
            // Check if package already exists in this transfer order
            PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
            PackageTransferLine.SetRange("Package No.", PackageTransferHeader.Barcode);
            if not PackageTransferLine.IsEmpty() then
                Error(PackageAlreadyReadErr);

            CreatePackageTransferLineFromPackageNoInfo(PackageTransferHeader, PackageNoInformation);
        end;

        PackageTransferHeader.Barcode := '';
    end;

    procedure ShipAndReceivePackageTransferHeader(var PackageTransferHeader: Record "Package Transfer Header MXW")
    var
        PackageTransferLine: Record "Package Transfer Line MXW";
        AtleastErr: Label 'You have to transfer at least one unit of: %1', Comment = '%1="Item No."';
    begin
        PackageTransferHeader.Validate(Shipped, true);
        PackageTransferHeader.Modify(true);

        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.FindSet();
        repeat
            if not (PackageTransferLine."Quantity To Transfer" > 0) then
                Error(AtleastErr, PackageTransferLine."Item No.");
        until PackageTransferLine.Next() = 0;

        CreateAndPostItemReclassificationJournal(PackageTransferHeader, false);
    end;

    local procedure CreatePackageTransferLineFromPackageNoInfo(var PackageTransferHeader: Record "Package Transfer Header MXW"; var PackageNoInformation: Record "Package No. Information")
    var
        PackageTransferLine: Record "Package Transfer Line MXW";
        Item: Record Item;
    begin
        PackageTransferLine.Init();
        PackageTransferLine."Document No." := PackageTransferHeader."No.";
        PackageTransferLine."Package No." := PackageNoInformation."Package No.";
        PackageTransferLine."Item No." := PackageNoInformation."Item No.";
        PackageTransferLine."Variant Code" := PackageNoInformation."Variant Code";
        PackageTransferLine."Lot No." := PackageNoInformation."Lot No. MXW";

        if Item.Get(PackageNoInformation."Item No.") then
            PackageTransferLine.Description := Item.Description;

        PackageTransferLine.Quantity := PackageNoInformation.Inventory;
        PackageTransferLine."Quantity To Transfer" := PackageNoInformation.Inventory;
        PackageTransferLine.Insert(true);
    end;

    procedure CreateAndPostItemReclassificationJournal(var PackageTransferHeader: Record "Package Transfer Header MXW"; HideDialog: Boolean)
    var
        ItemJournalTemplate: Record "Item Journal Template";
        ItemJournalBatch: Record "Item Journal Batch";
        ItemJournalLine: Record "Item Journal Line";
        PackageTransferLine: Record "Package Transfer Line MXW";
        ItemJnlPostLine: Codeunit "Item Jnl.-Post Line";
        LineNo: Integer;
        PackSuccessMsg: Label 'Package Transfer Order: %1 Successfully Received', Comment = '%1="Package Transfer Header MXW"."No."';
    begin
        // Find or create Item Journal Template and Batch
        ItemJournalTemplate.SetRange(Type, ItemJournalTemplate.Type::"Transfer");
        if ItemJournalTemplate.FindFirst() then begin
            ItemJournalBatch.SetRange("Journal Template Name", ItemJournalTemplate.Name);
            if not ItemJournalBatch.FindFirst() then begin
                ItemJournalBatch.Init();
                ItemJournalBatch."Journal Template Name" := ItemJournalTemplate.Name;
                ItemJournalBatch.Name := 'TRANSFER';
                ItemJournalBatch.Description := 'Package Transfer';
                ItemJournalBatch.Insert();
            end;
        end else begin
            // Create a basic transfer template
            ItemJournalTemplate.Init();
            ItemJournalTemplate.Name := 'TRANSFER';
            ItemJournalTemplate.Description := 'Transfer Journal';
            ItemJournalTemplate.Type := ItemJournalTemplate.Type::"Transfer";
            ItemJournalTemplate.Insert();

            ItemJournalBatch.Init();
            ItemJournalBatch."Journal Template Name" := ItemJournalTemplate.Name;
            ItemJournalBatch.Name := 'TRANSFER';
            ItemJournalBatch.Description := 'Package Transfer';
            ItemJournalBatch.Insert();
        end;

        // Delete existing lines
        ItemJournalLine.SetRange("Journal Template Name", ItemJournalBatch."Journal Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ItemJournalBatch.Name);
        ItemJournalLine.DeleteAll();

        LineNo := 10000;
        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.SetFilter("Quantity To Transfer", '>0');

        if PackageTransferLine.FindSet() then
            repeat
                // Create transfer journal line
                ItemJournalLine.Init();
                ItemJournalLine."Journal Template Name" := ItemJournalBatch."Journal Template Name";
                ItemJournalLine."Journal Batch Name" := ItemJournalBatch.Name;
                ItemJournalLine."Line No." := LineNo;
                ItemJournalLine."Entry Type" := ItemJournalLine."Entry Type"::Transfer;
                ItemJournalLine."Posting Date" := PackageTransferHeader."Posting Date";
                ItemJournalLine."Document No." := PackageTransferHeader."No.";
                ItemJournalLine.Validate("Item No.", PackageTransferLine."Item No.");
                ItemJournalLine.Validate("Variant Code", PackageTransferLine."Variant Code");
                ItemJournalLine.Validate("Location Code", PackageTransferHeader."Transfer-from Code");
                ItemJournalLine.Validate("New Location Code", PackageTransferHeader."Transfer-to Code");
                ItemJournalLine.Validate(Quantity, PackageTransferLine."Quantity To Transfer");
                ItemJournalLine."Lot No." := PackageTransferLine."Lot No.";
                ItemJournalLine."Package No." := PackageTransferLine."Package No.";
                ItemJournalLine.Insert();

                LineNo += 10000;
            until PackageTransferLine.Next() = 0;

        // Post the journal lines
        ItemJournalLine.SetRange("Journal Template Name", ItemJournalBatch."Journal Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", ItemJournalBatch.Name);
        if ItemJournalLine.FindSet() then
            repeat
                ItemJnlPostLine.RunWithCheck(ItemJournalLine);
            until ItemJournalLine.Next() = 0;

        // Update Package Transfer Header
        PackageTransferHeader.Validate(Received, true);
        PackageTransferHeader.Modify(true);

        if not HideDialog then
            Message(PackSuccessMsg, PackageTransferHeader."No.");
    end;

    [EventSubscriber(ObjectType::Page, Page::"Items by Location", OnAfterSetTempMatrixLocationFilters, '', false, false)]
    local procedure "Items by Location_OnAfterSetTempMatrixLocationFilters"(var Sender: Page "Items by Location"; var TempMatrixLocation: Record Location temporary)
    begin
        // Maxwell: Filter locations if needed
    end;
}
