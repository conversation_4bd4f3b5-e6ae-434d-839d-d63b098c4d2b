table 60020 "Package Transfer Header MXW"
{
    DataClassification = CustomerContent;
    Caption = 'Package Transfer Header';
    DrillDownPageId = "Package Transfer Orders MXW";
    LookupPageId = "Package Transfer Orders MXW";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            Editable = false;
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';

            trigger OnValidate()
            var
                MaxwellSetup: Record "Maxwell Setup MXW";
                NoSeries: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    MaxwellSetup.Get();
                    MaxwellSetup.TestField("Package Transfer Nos. MXW");
                    NoSeries.TestManual(MaxwellSetup."Package Transfer Nos. MXW");
                    "No. Series" := '';
                end;
            end;
        }

        field(2; "Transfer-from Code"; Code[10])
        {
            Caption = 'Transfer-from Code';
            ToolTip = 'Specifies the value of the Transfer-from Code field.';
            TableRelation = Location where("Use As In-Transit" = const(false));
        }

        field(4; "Transfer-to Code"; Code[10])
        {
            Caption = 'Transfer-to Code';
            TableRelation = Location where("Use As In-Transit" = const(false));
            ToolTip = 'Specifies the value of the Transfer-to Code field.';
        }

        field(6; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }

        field(7; Barcode; Code[50])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the value of the Barcode field.';

            trigger OnValidate()
            var
                PackageNoInformation: Record "Package No. Information";
            begin
                if PackageNoInformation.Get('', '', Rec.Barcode) then begin
                    MaxwellPackageTransMgt.ProcessBarcode(Rec);
                end;
            end;
        }

        field(8; Received; Boolean)
        {
            Caption = 'Received';
            ToolTip = 'Specifies the value of the Received field.';
        }

        field(11; "Total Transfer Quantity"; Decimal)
        {
            Caption = 'Total Transfer Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Package Transfer Line MXW".Quantity where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Transfer Quantity field.';
        }

        field(12; Shipped; Boolean)
        {
            Caption = 'Shipped';
            ToolTip = 'Specifies the value of the Shipped field.';
        }

        // field(13; "Is Production Location"; Boolean)
        // {
        //     Caption = 'Is Production Location';
        //     ToolTip = 'Specifies the value of the Is Production Location field.';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup(Location."Prod. Location MXW" where(Code = field("Transfer-to Code")));
        // }

        field(14; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            Editable = false;
            DataClassification = ToBeClassified;
            AllowInCustomizations = Always;
        }

        // field(15; "Package Transfer Type"; Enum "Package Transfer Type MXW")
        // {
        //     Caption = 'Package Transfer Type';
        //     Editable = false;
        //     DataClassification = ToBeClassified;
        //     AllowInCustomizations = Always;
        // }

        field(16; "Palette Count"; Integer)
        {
            Caption = 'Palette Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Transfer Line MXW" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Palette Count field.';
        }

        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(key2; "Source Document No.", Shipped, Received)
        {
        }
    }

    trigger OnInsert()
    var
        MaxwellSetup: Record "Maxwell Setup MXW";
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            MaxwellSetup.Get();
            MaxwellSetup.TestField("Package Transfer Nos. MXW");
            "No. Series" := MaxwellSetup."Package Transfer Nos. MXW";
            if NoSeries.AreRelated(MaxwellSetup."Package Transfer Nos. MXW", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;

        "Posting Date" := WorkDate();
    end;

    trigger OnDelete()
    var
        PackageTransferLine: Record "Package Transfer Line MXW";
    begin
        Rec.TestField(Received, false);
        PackageTransferLine.SetRange("Document No.", Rec."No.");
        PackageTransferLine.DeleteAll(true);
    end;

    var
        MaxwellPackageTransMgt: Codeunit "Maxwell Package Trans. Mgt MXW";
}
